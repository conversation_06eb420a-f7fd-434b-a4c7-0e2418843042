#![allow(non_snake_case)]
mod datasource;
mod model;
mod utils;

use std::{env, path::Path, thread, time::Duration};

use anyhow::Result;
use inotify::WatchMask;
use log::{debug, error, info, warn, LevelFilter};

use crate::{
    datasource::{
        config_parser::config_read,
        file_path::*,
        foreground_app::monitor_foreground_app,
        freq_table::gpufreq_table_init,
        load_monitor::utilization_init,
        node_monitor::{monitor_config, monitor_gaming},
    },
    model::gpu::GPU,
    utils::{
        constants::{strategy, InfoDisplay},
        file_operate::check_read_simple,
        file_status::get_status,
        inotify::InotifyWatcher,
        log_monitor::monitor_log_level,
        log_rotation::check_and_rotate_main_log,
        logger::{init_logger, read_log_level_config},
    },
};

/// 处理命令行参数
fn handle_command_line_args() -> Result<()> {
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 {
        match args[1].as_str() {
            "-h" => {
                InfoDisplay::print_usage();
                std::process::exit(0);
            }
            "-v" => {
                InfoDisplay::print_header();
                std::process::exit(0);
            }
            unknown => {
                println!("Unknown argument: {unknown}");
                println!("Use -h for help");
                std::process::exit(1);
            }
        }
    }
    Ok(())
}

/// 初始化GPU配置
fn initialize_gpu_config(gpu: &mut GPU) -> Result<()> {
    // 先初始化负载监控
    utilization_init()?;

    // 读取配置文件
    let config_file = CONFIG_FILE_TR;
    if Path::new(config_file).exists() {
        info!("Reading config file: {config_file}");
        config_read(config_file, gpu)
            .map_err(|e| anyhow::anyhow!("Failed to read config file: {}", e))?;
    } else {
        return Err(anyhow::anyhow!("Config file not found: {}", config_file));
    }

    // 初始化GPU频率表
    gpufreq_table_init(gpu)?;

    // 设置精确模式
    gpu.set_precise(get_status(DEBUG_DVFS_LOAD) || get_status(DEBUG_DVFS_LOAD_OLD));

    Ok(())
}

/// 启动监控线程
fn start_monitoring_threads(gpu: GPU) {
    // 游戏监控线程
    let gpu_clone1 = gpu.clone();
    thread::spawn(move || {
        if let Err(e) = monitor_gaming(gpu_clone1) {
            error!("Gaming monitor error: {e}");
        }
    });

    // 配置监控线程
    let gpu_clone2 = gpu.clone();
    thread::spawn(move || {
        if let Err(e) = monitor_config(gpu_clone2) {
            error!("Config monitor error: {e}");
        }
    });

    // 前台应用监控线程（延迟启动）
    thread::spawn(move || {
        info!(
            "Foreground app monitor will start in {} seconds",
            strategy::FOREGROUND_APP_STARTUP_DELAY
        );
        thread::sleep(Duration::from_secs(strategy::FOREGROUND_APP_STARTUP_DELAY));
        info!("Starting foreground app monitor now");

        if let Err(e) = monitor_foreground_app() {
            error!("Foreground app monitor error: {e}");
        }
    });

    // 日志等级监控线程
    thread::spawn(move || {
        if let Err(e) = monitor_log_level() {
            error!("Log level monitor error: {e}");
        }
    });

    // 日志轮转监控线程 - 只在debug日志等级时启动，并使用inotify实时响应日志等级变化
    thread::spawn(move || {
        if let Err(e) = monitor_log_rotation() {
            error!("Log rotation monitor error: {e}");
        }
    });
}

/// 配置GPU策略
fn configure_gpu_strategy(gpu: &mut GPU) {
    // 使用超简化的99%升频策略
    gpu.configure_strategy(
        0,                                 // 无余量
        1,                                 // 降频阈值
        strategy::SAMPLING_INTERVAL_120HZ, // 120Hz采样
        true,                              // 激进降频
    );

    // 其他策略设置
    gpu.frequency_strategy_mut().set_load_stability_threshold(1);
    gpu.frequency_strategy_mut().set_adaptive_sampling(
        false,
        strategy::SAMPLING_INTERVAL_120HZ,
        strategy::SAMPLING_INTERVAL_120HZ,
    );
}

/// 显示系统信息
fn display_system_info(gpu: &GPU) {
    info!("Monitor Inited");
    info!("{MAIN_THREAD} Start");

    // 频率信息
    info!("BootFreq: {}KHz", gpu.get_cur_freq());
    info!(
        "Driver: gpufreq{}",
        if gpu.is_gpuv2() { "v2" } else { "v1" }
    );
    info!(
        "Is Precise: {}",
        if gpu.is_precise() { "Yes" } else { "No" }
    );
    info!("Max Freq: {}KHz", gpu.get_max_freq());
    info!("Middle Freq: {}KHz", gpu.get_middle_freq());
    info!("Min Freq: {}KHz", gpu.get_min_freq());
    info!("Current Margin: {}%", gpu.get_margin());

    // DCS信息
    if gpu.is_gpuv2() {
        info!(
            "DCS: {}",
            if gpu.is_dcs_enabled() {
                "Enabled"
            } else {
                "Disabled"
            }
        );
        info!(
            "V2 Driver Down Threshold: {} times",
            gpu.get_down_threshold()
        );
    }

    // DDR频率信息
    display_ddr_info(gpu);

    // 策略信息
    info!("Using ultra-simplified strategy: Load >= 99% = upgrade, Load < 99% = downscale");
    info!(
        "Second highest frequency: {}KHz",
        gpu.get_second_highest_freq()
    );
}

/// 显示DDR相关信息
fn display_ddr_info(gpu: &GPU) {
    if gpu.is_ddr_freq_fixed() {
        info!(
            "DDR Frequency: Fixed at {}",
            gpu.ddr_manager().get_ddr_freq()
        );
    } else {
        info!("DDR Frequency: Auto mode");
    }

    match gpu.ddr_manager().get_ddr_freq_table() {
        Ok(freq_table) => {
            info!("Available DDR frequency options:");
            for (i, (opp, desc)) in freq_table.iter().enumerate().take(3) {
                info!("  Option {}: OPP={}, Description: {}", i + 1, opp, desc);
            }
            if freq_table.len() > 3 {
                info!("  ... and {} more options", freq_table.len() - 3);
            }
        }
        Err(e) => {
            warn!("Failed to get DDR frequency table: {e}");
        }
    }

    if gpu.is_gpuv2() {
        let ddr_freqs = gpu.ddr_manager().get_ddr_v2_supported_freqs();
        if !ddr_freqs.is_empty() {
            info!("V2 driver supported DDR frequencies: {ddr_freqs:?}");
        }

        let gpu_freqs = gpu.get_v2_supported_freqs();
        if !gpu_freqs.is_empty() {
            info!("V2 driver supported GPU frequencies: {gpu_freqs:?}");
        }
    }
}

fn main() -> Result<()> {
    // 处理命令行参数
    handle_command_line_args()?;

    // 初始化日志
    init_logger()?;
    InfoDisplay::print_header();

    // 初始化GPU
    let mut gpu = GPU::new();
    info!("Loading");

    // 初始化GPU配置
    initialize_gpu_config(&mut gpu)?;

    // 启动监控线程
    start_monitoring_threads(gpu.clone());

    // 等待线程启动
    thread::sleep(Duration::from_secs(5));

    // 初始化频率和电压
    gpu.set_cur_freq(gpu.get_freq_by_index(0));
    gpu.frequency_mut().gen_cur_volt();

    // 配置策略
    configure_gpu_strategy(&mut gpu);

    // 显示系统信息
    display_system_info(&gpu);

    info!("Advanced GPU Governor Started");

    // 开始频率调整
    gpu.adjust_gpufreq()
}

/// 监控日志轮转 - 使用inotify实时响应日志等级变化
fn monitor_log_rotation() -> Result<()> {
    info!("Log rotation monitor thread started");

    // 检查日志等级文件路径
    if !check_read_simple(LOG_LEVEL_PATH) {
        info!("Log level file does not exist: {LOG_LEVEL_PATH}");
    } else {
        info!("Using log level path for rotation monitor: {LOG_LEVEL_PATH}");
    }

    // 设置文件监控
    let mut inotify = InotifyWatcher::new()?;
    inotify.add(LOG_LEVEL_PATH, WatchMask::CLOSE_WRITE | WatchMask::MODIFY)?;

    // 初始状态检查
    let mut rotation_active = match read_log_level_config() {
        Ok(level) => {
            let should_be_active = level == LevelFilter::Debug;
            if should_be_active {
                info!("Log rotation monitor enabled (debug level detected at startup)");
            } else {
                info!("Log rotation monitor disabled (non-debug level at startup)");
            }
            should_be_active
        }
        Err(e) => {
            warn!("Failed to read initial log level config for rotation monitor: {e}");
            false
        }
    };

    // 主循环
    loop {
        // 等待文件变化事件
        inotify.wait_and_handle()?;

        // 检查文件是否存在
        if !check_read_simple(LOG_LEVEL_PATH) {
            debug!("Log level file no longer exists");
            if rotation_active {
                info!("Log rotation monitor disabled (log level file no longer exists)");
                rotation_active = false;
            }
            continue;
        }

        // 读取新的日志等级配置
        match read_log_level_config() {
            Ok(level) => {
                let should_be_active = level == LevelFilter::Debug;

                // 检查状态变化
                if should_be_active && !rotation_active {
                    info!("Log rotation monitor enabled (debug level detected)");
                    rotation_active = true;
                } else if !should_be_active && rotation_active {
                    info!("Log rotation monitor disabled (debug level no longer active)");
                    rotation_active = false;
                }

                // 只在debug等级时执行日志轮转检查
                if rotation_active {
                    match check_and_rotate_main_log() {
                        Ok(rotated) => {
                            if rotated {
                                info!("Log file rotated successfully");
                            }
                        }
                        Err(e) => {
                            warn!("Failed to check/rotate log file: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                warn!(
                    "Failed to read log level config for rotation monitor: {}",
                    e
                );
                if rotation_active {
                    info!("Log rotation monitor disabled due to config error");
                    rotation_active = false;
                }
            }
        }
    }
}
